<div class="flex flex-wrap -mx-2">
  <div class="md:w-6/12 px-2" style="margin: auto">
    <div class="title">
      <h5 class="underline underline-offset-[5px] !font-normal decoration-lime">Payment Reports</h5>
    </div>
  </div>
  <div class="md:w-6/12 px-2 text-right" style="margin: auto"></div>
</div>

<div class="flex flex-wrap -mx-2">
  <div class="lg:w-9/12 px-2 sm:w-full px-2 my-[15px] items-rows">
    <div>
      <nb-select
        fullWidth
        placeholder="Report Type"
        name="reportType"
        id="reportType"
        [selected]="selectedReportType"
        (selectedChange)="onReportTypeChange($event)"
      >
        <nb-option *ngFor="let reportType of reportTypes" [value]="reportType.id">
          {{ reportType.name }}
        </nb-option>
      </nb-select>
    </div>

    <div>
      <div class="position-relative">
        <input
          nbInput
          placeholder="Date Range"
          [nbDatepicker]="dateRangeReportPicker"
          class="w-[250px]"
          #dateRangeInput
        />
        <!-- Clear Icon (shown when input has value or item is selected) -->
        <button
          *ngIf="showClearDateRangeIcon"
          nbButton
          ghost
          size="medium"
          class="clear-icon"
          type="button"
          (click)="onClearDateRange($event)"
          title="Clear date range"
        >
          <nb-icon icon="close-outline" pack="eva"></nb-icon>
        </button>
      </div>
      <nb-rangepicker
        #dateRangeReportPicker
        (rangeChange)="onDateRangeChange($event)"
        [(range)]="dateRangeValue"
      ></nb-rangepicker>
    </div>

    <div class="search-filter">
      <nb-form-field>
        <nb-icon nbSuffix icon="search-outline" pack="eva"></nb-icon>
        <input
          type="text"
          fullWidth
          placeholder="Enter reference..."
          (input)="filterGlobal($event)"
          nbInput
          #searchInput
        />
      </nb-form-field>
    </div>
  </div>

  <div class="lg:w-3/12 px-2 my-[15px] text-right">
    <button
      nbButton
      ghost
      status="primary"
      *ngIf="!isManager"
      (click)="onExportReportToPDF()"
      title="Export to PDF"
      [disabled]="isDisabledExport"
    >
      <i class="pi pi-download"></i>
    </button>
  </div>
</div>

<div *ngIf="reports">
  <nb-card>
    <nb-card-body>
      <p-table
        #dt
        [filterDelay]="700"
        [value]="reports"
        [lazy]="true"
        [loading]="loading"
        (onLazyLoad)="nextPage($event)"
        [paginator]="false"
        [totalRecords]="totalRecords"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} to {last} of {totalRecords} records"
        [rowsPerPageOptions]="[10, 25, 50]"
        [scrollable]="true"
        scrollWidth="flex"
        scrollHeight="flex"
        [globalFilterFields]="['id', 'reference']"
        sortField="id"
        [sortOrder]="-1"
      >
        <ng-template pTemplate="header">
          <!-- Investment Portfolio Headers -->
          <tr *ngIf="selectedReportType === 'investment-portfolio'">
            <th style="min-width: 120px" [pSortableColumn]="'loanAccount'">
              <div>
                <div>Loan Account</div>
                <p-sortIcon [field]="'loanAccount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 200px" [pSortableColumn]="'borrowerName'">
              <div>
                <div>Borrower Name</div>
                <p-sortIcon [field]="'borrowerName'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'pctOwned'">
              <div>
                <div>Pct Owned</div>
                <p-sortIcon [field]="'pctOwned'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'interestRate'">
              <div>
                <div>Interest Rate</div>
                <p-sortIcon [field]="'interestRate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'maturityDate'">
              <div>
                <div>Maturity Date</div>
                <p-sortIcon [field]="'maturityDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'termLeft'">
              <div>
                <div>Term Left</div>
                <p-sortIcon [field]="'termLeft'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'nextPayment'">
              <div>
                <div>Next Payment</div>
                <p-sortIcon [field]="'nextPayment'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'regularPayment'">
              <div>
                <div>Regular Payment</div>
                <p-sortIcon [field]="'regularPayment'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'loanBalance'">
              <div>
                <div>Loan Balance</div>
                <p-sortIcon [field]="'loanBalance'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Funding Activity Headers -->
          <tr *ngIf="selectedReportType === 'funding-activity'">
            <th style="min-width: 120px" [pSortableColumn]="'transactionDate'">
              <div>
                <div>Transaction Date</div>
                <p-sortIcon [field]="'transactionDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'reference'">
              <div>
                <div>Reference</div>
                <p-sortIcon [field]="'reference'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'loanAccount'">
              <div>
                <div>Loan Account</div>
                <p-sortIcon [field]="'loanAccount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 200px" [pSortableColumn]="'borrowerName'">
              <div>
                <div>Borrower Name</div>
                <p-sortIcon [field]="'borrowerName'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'amountFunded'">
              <div>
                <div>Amount Funded</div>
                <p-sortIcon [field]="'amountFunded'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Account Activity Headers -->
          <tr *ngIf="selectedReportType === 'account-activity'">
            <th style="min-width: 120px" [pSortableColumn]="'transactionDate'">
              <div>
                <div>Transaction Date</div>
                <p-sortIcon [field]="'transactionDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'reference'">
              <div>
                <div>Reference</div>
                <p-sortIcon [field]="'reference'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'loanAccount'">
              <div>
                <div>Loan Account</div>
                <p-sortIcon [field]="'loanAccount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'transactionAmount'">
              <div>
                <div>Transaction Amount</div>
                <p-sortIcon [field]="'transactionAmount'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'servFees'">
              <div>
                <div>Serv. Fees</div>
                <p-sortIcon [field]="'servFees'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 80px" [pSortableColumn]="'gst'">
              <div>
                <div>GST</div>
                <p-sortIcon [field]="'gst'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'interest'">
              <div>
                <div>Interest</div>
                <p-sortIcon [field]="'interest'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'distributionPrincipal'">
              <div>
                <div>Distribution Principal</div>
                <p-sortIcon [field]="'distributionPrincipal'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'charges'">
              <div>
                <div>Charges</div>
                <p-sortIcon [field]="'charges'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 80px" [pSortableColumn]="'other'">
              <div>
                <div>Other</div>
                <p-sortIcon [field]="'other'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'escrow'">
              <div>
                <div>Escrow</div>
                <p-sortIcon [field]="'escrow'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Trust Activity Headers -->
          <tr *ngIf="selectedReportType === 'trust-activity'">
            <th style="min-width: 120px" [pSortableColumn]="'transactionDate'">
              <div>
                <div>Transaction Date</div>
                <p-sortIcon [field]="'transactionDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'reference'">
              <div>
                <div>Reference</div>
                <p-sortIcon [field]="'reference'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 200px" [pSortableColumn]="'fromWhomReceivedOrToWhomPaid'">
              <div>
                <div>From Whom Received or To Whom Paid</div>
                <p-sortIcon [field]="'fromWhomReceivedOrToWhomPaid'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 200px" [pSortableColumn]="'descriptionMemo'">
              <div>
                <div>Description / Memo</div>
                <p-sortIcon [field]="'descriptionMemo'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'amountPaidOut'">
              <div>
                <div>Amount Paid Out</div>
                <p-sortIcon [field]="'amountPaidOut'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'amountReceived'">
              <div>
                <div>Amount Received</div>
                <p-sortIcon [field]="'amountReceived'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 120px" [pSortableColumn]="'dailyBalance'">
              <div>
                <div>Daily Balance</div>
                <p-sortIcon [field]="'dailyBalance'"></p-sortIcon>
              </div>
            </th>
          </tr>

          <!-- Default Headers for other report types -->
          <!-- <tr *ngIf="selectedReportType !== 'investment-portfolio' && selectedReportType !== 'account-activity'">
            <th style="min-width: 100px; max-width: 100px" [pSortableColumn]="'id'">
              <div>
                <div>ID</div>
                <p-sortIcon [field]="'id'"></p-sortIcon>
              </div>
            </th>

            <th style="width: 250px" [pSortableColumn]="'description'">
              <div>
                <div>Description</div>
                <p-sortIcon [field]="'description'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 150px" [pSortableColumn]="'paymentDate'">
              <div>
                <div>Date</div>
                <p-sortIcon [field]="'paymentDate'"></p-sortIcon>
              </div>
            </th>

            <th style="min-width: 100px" [pSortableColumn]="'amount'">
              <div>
                <div>Amount</div>
                <p-sortIcon [field]="'amount'"></p-sortIcon>
              </div>
            </th>
          </tr> -->
        </ng-template>

        <ng-template pTemplate="body" let-report>
          <!-- Investment Portfolio Body -->
          <tr *ngIf="selectedReportType === 'investment-portfolio'">
            <td style="min-width: 120px">{{ report.loanNumber }}</td>
            <td style="min-width: 200px">{{ report.borrowerName }}</td>
            <td style="min-width: 100px">{{ report.pctOwned | number: "1.3-3" }}%</td>
            <td style="min-width: 100px">{{ report.lenderRate | number: "1.3-3" }}%</td>
            <td style="min-width: 120px">{{ report.maturityDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 100px">{{ report.termLeft }}</td>
            <td style="min-width: 120px">{{ report.nextPayment | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 120px">{{ report.regularPayment | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 120px">{{ report.principalBalance | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Funding Activity Body -->
          <tr *ngIf="selectedReportType === 'funding-activity'">
            <td style="min-width: 120px">{{ report.paymentDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 150px">{{ report.title }}</td>
            <td style="min-width: 120px">{{ report.loanNumber }}</td>
            <td style="min-width: 200px">{{ report.borrower }}</td>
            <td style="min-width: 120px">{{ report.amount | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Account Activity Body -->
          <tr *ngIf="selectedReportType === 'account-activity'">
            <td style="min-width: 120px">{{ report.transactionDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 120px">{{ report.reference }}</td>
            <td style="min-width: 120px">{{ report.loanAccount }}</td>
            <td style="min-width: 150px">{{ report.transactionAmount | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.servFees | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ report.gst | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.interest | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 150px">{{ report.distributionPrincipal | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.charges | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ report.other | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ report.escrow | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Trust Activity Body -->
          <tr *ngIf="selectedReportType === 'trust-activity'">
            <td style="min-width: 120px">{{ report.transactionDate | date: "dd/MM/yyyy" }}</td>
            <td style="min-width: 150px">{{ report.reference }}</td>
            <td style="min-width: 200px">{{ report.fromWhomReceivedOrToWhomPaid }}</td>
            <td style="min-width: 200px">{{ report.descriptionMemo }}</td>
            <td style="min-width: 120px">{{ report.amountPaidOut | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 120px">{{ report.amountReceived | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 120px">{{ report.dailyBalance | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>

          <!-- Default Body for other report types -->
          <!-- <tr *ngIf="selectedReportType !== 'investment-portfolio' && selectedReportType !== 'account-activity'">
            <td style="min-width: 100px; max-width: 100px">{{ report.id }}</td>
            <td style="width: 250px">{{ report.description }}</td>
            <td style="min-width: 150px">{{ report.paymentDate | date: "dd/MM/YYYY" }}</td>
            <td style="min-width: 100px">{{ report.amount | currency: "USD" : "symbol" : "1.0" }}</td>
          </tr> -->
        </ng-template>

        <ng-template #loadingbody let-columns="columns">
          <tr style="height: 46px">
            <td *ngFor="let col of columns; let even = even">
              <p-skeleton [ngStyle]="{ width: even ? (col.field === 'year' ? '30%' : '40%') : '60%' }" />
            </td>
          </tr>
        </ng-template>

        <!-- Current Portfolio Yield Total Row - Outside of body template -->
        <ng-template #footer *ngIf="selectedReportType === 'investment-portfolio' && reports && reports.length > 0">
          <tr class="font-bold">
            <td colspan="4"></td>
            <td colspan="3" style="text-align: start">
              Current Portfolio Yield: {{ getTotalInterestRate() | number: "1.3-3" }}%
            </td>
            <td style="min-width: 120px">
              {{ getTotalRegularPayment() | currency: "USD" : "symbol" : "1.2-2" }}
            </td>
            <td style="min-width: 120px">
              {{ getTotalLoanBalance() | currency: "USD" : "symbol" : "1.2-2" }}
            </td>
          </tr>
        </ng-template>

        <!-- Funding Activity Total Row - Outside of body template -->
        <ng-template #footer *ngIf="selectedReportType === 'funding-activity' && reports && reports.length > 0">
          <tr class="font-bold">
            <td colspan="4"></td>
            <td style="min-width: 120px">{{ getTotalAmountFunded() | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>
        </ng-template>

        <!-- Account Activity Total Row - Outside of body template -->
        <ng-template #footer *ngIf="selectedReportType === 'account-activity' && reports && reports.length > 0">
          <tr class="font-bold">
            <td colspan="3"></td>
            <td style="min-width: 150px">{{ getTotalTransactionAmount() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ getTotalServFees() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ getTotalGst() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ getTotalInterest() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 150px">
              {{ getTotalDistributionPrincipal() | currency: "USD" : "symbol" : "1.2-2" }}
            </td>
            <td style="min-width: 100px">{{ getTotalCharges() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 80px">{{ getTotalOther() | currency: "USD" : "symbol" : "1.2-2" }}</td>
            <td style="min-width: 100px">{{ getTotalEscrow() | currency: "USD" : "symbol" : "1.2-2" }}</td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
          <tr>
            <td
              style="text-align: center"
              [attr.colspan]="
                selectedReportType === 'investment-portfolio'
                  ? 9
                  : selectedReportType === 'account-activity'
                    ? 11
                    : selectedReportType === 'funding-activity'
                      ? 5
                      : selectedReportType === 'trust-activity'
                        ? 8
                        : 4
              "
            >
              No reports yet.
            </td>
          </tr>
        </ng-template>
      </p-table>
    </nb-card-body>
  </nb-card>
</div>
