<form class="w-full px-2" [formGroup]="userForm" (ngSubmit)="onSubmit()">
  <nb-card>
    <nb-card-header>
      <div class="flex flex-wrap -mx-2">
        <h5 class="w-6/12 px-2">
          <div class="title">
            {{ title }}
          </div>
        </h5>

        <div class="w-6/12 px-2">
          <div class="popup-close float-right">
            <button ghost nbButton (click)="close()">
              <nb-icon icon="close"></nb-icon>
            </button>
          </div>
        </div>
      </div>
    </nb-card-header>

    <nb-card-body>
      <div style="width: 800px; margin: 22px 50px">
        <h5>User Information</h5>

        <div class="w-full px-2 my-[15px]">
          <nb-alert *ngIf="error" accent="danger">{{ error }}</nb-alert>
        </div>

        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong> First name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="text"
              fieldSize="large"
              formControlName="firstName"
              status="{{ submitted && f.firstName.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.firstName.errors" class="invalid-feedback">
              <div *ngIf="f.firstName.errors.required">First name is required.</div>
            </div>
          </div>

          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong>Last name </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="text"
              fieldSize="large"
              formControlName="lastName"
              status="{{ submitted && f.lastName.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.lastName.errors" class="invalid-feedback">
              <div *ngIf="f.lastName.errors.required">Last name is required.</div>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label> <strong> Email </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              placeholder="<EMAIL>"
              shape="semi-round"
              type="text"
              fieldSize="large"
              formControlName="email"
              status="{{ submitted && f.email.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.email.errors" class="invalid-feedback">
              <div *ngIf="f.email.errors.required">Email is required.</div>
              <div *ngIf="f.email.errors.pattern">Invalid email format.</div>
            </div>
          </div>

          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label> <strong> Mobile </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <nb-form-field>
              <span nbPrefix class="flagIcon">
                <p-select
                  [options]="countries"
                  optionLabel="name"
                  [(ngModel)]="selectedCountry"
                  formControlName="countryCode"
                  [filter]="true"
                  filterBy="name"
                  styleClass="shadow-none"
                >
                  <ng-template pTemplate="selectedItem">
                    <div class="flex items-center gap-1 text-velvet-700" *ngIf="selectedCountry">
                      <img
                        src="https://flagcdn.com/16x12/{{ selectedCountry.code.toLowerCase() }}.png"
                        width="16"
                        height="12"
                        alt="{{ selectedCountry.code }}"
                      />
                      {{ selectedCountry.dial_code }}
                    </div>
                  </ng-template>
                  <ng-template let-country pTemplate="item">
                    <div class="flex items-center gap-1 text-velvet-700">
                      <img
                        src="https://flagcdn.com/16x12/{{ country.code.toLowerCase() }}.png"
                        width="16"
                        height="12"
                        alt="{{ country.code }}"
                      />
                      {{ country.name }} {{ country.dial_code }}
                    </div>
                  </ng-template>
                </p-select>
              </span>
              <input
                nbInput
                fullWidth
                placeholder="4XXXXXXXX"
                mask="X00000000"
                [patterns]="pattern"
                [dropSpecialCharacters]="false"
                maxlength="10"
                minlength="9"
                shape="semi-round"
                type="text"
                fieldSize="large"
                formControlName="mobile"
                status="{{ submitted && f.mobile.errors ? 'danger' : 'basic' }}"
              />
            </nb-form-field>

            <div *ngIf="submitted && f.mobile.errors" class="invalid-feedback">
              <div *ngIf="f.mobile.errors.required">Mobile is required.</div>
              <div *ngIf="f.mobile.errors.pattern">Invalid mobile format.</div>
              <div *ngIf="f.mobile.errors.mask">Invalid mobile format.</div>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap -mx-2">
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong> User Type </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="text"
              fieldSize="large"
              readonly
              [value]="'Staff'"
              status="basic"
              disabled
            />
          </div>

          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label> <strong> User Role </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="text"
              fieldSize="large"
              readonly
              [value]="'Administrator'"
              status="basic"
              disabled
            />
          </div>

          <div
            class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]"
            *ngIf="f.userTypeId.value === UserTypes.Lender"
          >
            <label> <strong> Organisation </strong> <strong class="text-lime required"> &nbsp; * </strong> </label>

            <nb-select
              fullWidth
              size="large"
              shape="semi-round"
              status="basic"
              name="lenderOrgId"
              formControlName="lenderOrgId"
            >
              <nb-option *ngFor="let org of orgData" [value]="org.id">
                {{ org.name }}
              </nb-option>
            </nb-select>

            <div *ngIf="submitted && f.roleId.errors" class="invalid-feedback">
              <div *ngIf="f.roleId.errors.required">Organisation is required.</div>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap -mx-2" *ngIf="creditAdvisor">
          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong> Broker ID </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="text"
              fieldSize="large"
              formControlName="brokerId"
              status="{{ submitted && f.brokerId.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.brokerId.errors" class="invalid-feedback">
              <div *ngIf="f.brokerId.errors.required">Brokey ID is required.</div>
            </div>
          </div>

          <div class="lg:w-6/12 px-2 sm:w-full px-2 w-full px-2 my-[15px]">
            <label><strong>Broker Key </strong> <strong class="text-lime required"> &nbsp; * </strong></label>
            <input
              nbInput
              fullWidth
              placeholder=""
              shape="semi-round"
              type="password"
              fieldSize="large"
              formControlName="brokerKey"
              status="{{ submitted && f.brokerKey.errors ? 'danger' : 'basic' }}"
            />
            <div *ngIf="submitted && f.brokerKey.errors" class="invalid-feedback">
              <div *ngIf="f.brokerKey.errors.required">Broker Key is required.</div>
            </div>
          </div>
        </div>
        <br />

        <!-- [disabled]="!userForm.valid" -->
      </div>
    </nb-card-body>
    <nb-card-footer>
      <div class="w-full px-2">
        <button class="float-right" [nbSpinner]="loading" nbButton status="primary" style="min-width: 135px">
          Save
        </button>
      </div>
    </nb-card-footer>
  </nb-card>
</form>
