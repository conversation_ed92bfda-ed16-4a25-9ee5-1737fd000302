import { CommonModule, DatePipe } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ReportType } from '@core/models/config';
import { HttpResponse } from '@core/models/response/http.response';
import {
  ReportsFundingActivityPayload,
  ReportsInvestmentPortfolioPayload,
} from '@core/models/response/reports.reponse';
import { InvestorsService } from '@core/services/investors.service';
import { Filters, SharedService } from '@core/services/shared.service';
import {
  NbButtonModule,
  NbCardModule,
  NbDatepickerModule,
  NbFormFieldModule,
  NbIconModule,
  NbInputModule,
  NbSelectModule,
  NbToastrService,
} from '@nebular/theme';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-investor-reports',
  templateUrl: './investor-reports.component.html',
  styleUrls: ['./investor-reports.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NbCardModule,
    NbIconModule,
    NbSelectModule,
    NbFormFieldModule,
    NbInputModule,
    TableModule,
    NbButtonModule,
    NbDatepickerModule,
  ],
})
export class InvestorReportsComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('dt') dt: any;
  @ViewChild('searchInput') searchInput!: ElementRef;
  @ViewChild('dateRangeInput') dateRangeInput!: ElementRef;
  reports: any[] = [];
  totalRecords = 0;
  filterParams: Filters = {};
  loading = false;
  firstLoad = true;
  isDisabledExport = false;
  searchText = '';
  dateRangeValue = { start: new Date(), end: new Date() };
  showClearDateRangeIcon = false;
  // Report types
  selectedReportType = ReportType.InvestmentPortfolio;
  reportTypes = [
    { id: 'investment-portfolio', name: 'Investment Portfolio', icon: 'briefcase-outline' },
    { id: 'funding-activity', name: 'Funding Activity', icon: 'trending-up-outline' },
    { id: 'account-activity', name: 'Account Activity', icon: 'activity-outline' },
    { id: 'trust-activity', name: 'Trust Activity', icon: 'shield-outline' },
  ];

  first = 0;

  investorId: number | undefined;
  destroyed$ = new Subject<boolean>();
  datepipe = inject(DatePipe);
  toast = inject(NbToastrService);
  sharedService = inject(SharedService);
  investorsService = inject(InvestorsService);
  cdr = inject(ChangeDetectorRef);

  async ngOnInit(): Promise<void> {
    this.filterParams = {
      pageNumber: 0,
      pageSize: 20,
    } as Filters;
    if (this.isInvestor) {
      this.investorId = this.investorsService.accountValue?.investorId;
    }
  }

  /** Fix for "ExpressionChangedAfterItHasBeenCheckedError: Expression has changed after it was checked." */
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  private getList(): void {
    this.loading = true;
    this.reports = [];
    if (this.isInvestor) {
      this.filterParams.investorId = this.investorId;
    }

    switch (this.selectedReportType) {
      case ReportType.InvestmentPortfolio:
        this.getReportsInvestmentsPortfolio();
        break;
      case ReportType.FundingActivity:
        this.getReportsFundingActivity();
        break;
      case ReportType.AccountActivity:
        // Handle account activity report
        this.reports = this.transformToAccountData([]);
        this.loading = false;
        break;
      case ReportType.TrustActivity:
        // Handle trust activity report
        this.reports = this.transformToTrustData([]);
        this.loading = false;
        break;
    }
  }

  getReportsFundingActivity(): void {
    this.investorsService
      .getReportsFundingActivity(this.filterParams)
      .subscribe((data: HttpResponse<ReportsFundingActivityPayload>) => {
        if (data.success) {
          this.reports = data.payload.investments;
          this.totalRecords = data.payload.rows;
          this.loading = false;
        }
      });
  }

  getReportsInvestmentsPortfolio(): void {
    this.investorsService
      .getReportsInvestmentPortfolio(this.filterParams)
      .subscribe((data: HttpResponse<ReportsInvestmentPortfolioPayload>) => {
        if (data.success) {
          this.reports = data.payload.investments;
          this.totalRecords = data.payload.rows;
          this.loading = false;
        }
      });
  }

  private transformToAccountData(data: any[]): any[] {
    // Transform to account activity structure matching the image data
    const mockData = [
      {
        id: 1,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 713.33,
        servFees: 0,
        gst: 0,
        interest: 713.33,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 3,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: 5000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 5000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 4,
        transactionDate: new Date('2024-05-02'),
        reference: '0062094',
        loanAccount: 'MA00100702',
        transactionAmount: -1.47,
        servFees: 0,
        gst: 0,
        interest: -1.47,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 5,
        transactionDate: new Date('2024-06-28'),
        reference: '0063349',
        loanAccount: 'MA00100702',
        transactionAmount: 668.75,
        servFees: 0,
        gst: 0,
        interest: 668.75,
        distributionPrincipal: 0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 6,
        transactionDate: new Date('2024-06-28'),
        reference: '0063349',
        loanAccount: 'MA00100702',
        transactionAmount: 11000.0,
        servFees: 0,
        gst: 0,
        interest: 0,
        distributionPrincipal: 11000.0,
        charges: 0,
        other: 0,
        escrow: 0,
        description: 'MA00100702',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      checkoutOrLoan: item.transactionType || 'N/A',
      loanAccount: item.loanAccount || 'N/A',
      transactionAmount: item.amount || 0,
      servFees: 0,
      gst: 0,
      interest: item.transactionType === 'Interest' ? item.amount : 0,
      distributionPrincipal: item.transactionType === 'Distribution' ? item.amount : 0,
      charges: 0,
      other: 0,
      escrow: 0,
    }));
  }

  private transformToTrustData(data: any[]): any[] {
    // Transform to trust structure matching the image data
    const mockTrustData = [
      {
        id: 1,
        transactionDate: new Date('2024-04-30'),
        reference: 'APRIL',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 1.47,
        amountReceived: 0.0,
        dailyBalance: -1.47,
        description: 'Trust Activity Entry',
        investment: 'Sample Investment',
        investor: 'Sample Investor',
      },
      {
        id: 2,
        transactionDate: new Date('2024-04-30'),
        reference: 'APRIL',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 711.86,
        description: 'Trust Activity Entry 2',
        investment: 'Sample Investment 2',
        investor: 'Sample Investor 2',
      },
      {
        id: 3,
        transactionDate: new Date('2024-04-30'),
        reference: 'DECEMBER',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 1425.19,
        description: 'Trust Activity Entry 3',
        investment: 'Sample Investment 3',
        investor: 'Sample Investor 3',
      },
      {
        id: 4,
        transactionDate: new Date('2024-04-30'),
        reference: 'FEBRUARY',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 2138.52,
        description: 'Trust Activity Entry 4',
        investment: 'Sample Investment 4',
        investor: 'Sample Investor 4',
      },
      {
        id: 5,
        transactionDate: new Date('2024-04-30'),
        reference: 'JANUARY',
        fromWhomReceivedOrToWhomPaid: 'Qartaba Homes Pty Ltd',
        descriptionMemo: 'Borrower Payment',
        amountPaidOut: 0.0,
        amountReceived: 713.33,
        dailyBalance: 2851.85,
        description: 'Trust Activity Entry 5',
        investment: 'Sample Investment 5',
        investor: 'Sample Investor 5',
      },
    ];

    // Return mock data if no real data, otherwise transform real data
    if (!data || data.length === 0) {
      return mockTrustData;
    }

    return data.map((item) => ({
      ...item,
      transactionDate: item.paymentDate || new Date(),
      checkOrReference: item.transactionType || 'N/A',
      fromWhomReceivedOrToWhomPaid: item.investor || 'N/A',
      descriptionMemo: item.description || 'N/A',
      balanceForward: item.balanceForward || 0,
      amountPaidOut: item.amountPaidOut || 0,
      amountReceived: item.amountReceived || 0,
      dailyBalance: item.dailyBalance || 0,
    }));
  }

  get isInvestor(): boolean {
    return this.sharedService.isInvestor();
  }

  get isManager(): boolean {
    return this.sharedService.isManager();
  }

  get isAdmin(): boolean {
    return this.sharedService.isAdmin();
  }

  nextPage(event: TableLazyLoadEvent): void {
    this.filterParams = this.sharedService.getFiltersFromDataTable(event, this.filterParams);
    this.getList();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(true);
    this.destroyed$.unsubscribe();
  }

  filterGlobal(event: any): void {
    this.dt.filterGlobal(event.target.value, 'contains');
  }

  onReportTypeChange(reportType: ReportType): void {
    if (this.selectedReportType === reportType) {
      return;
    }
    this.selectedReportType = reportType;
    this.getList();
    // this.clearFilters();
  }

  clearFilters(): void {
    this.reports = [];
    this.dt.clear();
    this.searchInput.nativeElement.value = '';
    this.dateRangeInput.nativeElement.value = '';
    this.dateRangeValue = { start: new Date(), end: new Date() };
    this.showClearDateRangeIcon = false;
  }

  exportReport(): void {
    this.toast.default(`Coming soon...`, '', {
      icon: 'download',
    });
    // Use payments export for now, in real implementation you'd have a reports export
    // this.investorsService.getPaymentsExport(this.filterParams);
  }

  applyFilters(): void {
    this.getList();
  }

  deleteReport(reportId: number): void {
    // Placeholder for delete functionality
    // In a real implementation, you would have a delete dialog similar to payments
    console.log('Delete report:', reportId);
  }

  getTotalInterestRate(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    // Calculate weighted average interest rate based on loan balance
    // let totalWeightedRate = 0;
    // let totalBalance = 0;
    let totalRate = 0;

    this.reports.forEach((report) => {
      // const balance = report.principalBalance || 0;
      const rate = report.lenderRate || 0;
      // totalWeightedRate += balance * rate;
      // totalBalance += balance;
      totalRate += rate;
    });

    // return totalBalance > 0 ? totalWeightedRate / totalBalance : 0;
    return totalRate / this.reports.length;
  }

  getTotalRegularPayment(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.regularPayment || 0);
    }, 0);
  }

  getTotalLoanBalance(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.principalBalance || 0);
    }, 0);
  }

  getTotalAmountFunded(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.amount || 0);
    }, 0);
  }

  // Account Activity Total Methods
  getTotalTransactionAmount(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.transactionAmount || 0);
    }, 0);
  }

  getTotalServFees(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.servFees || 0);
    }, 0);
  }

  getTotalGst(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.gst || 0);
    }, 0);
  }

  getTotalInterest(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.interest || 0);
    }, 0);
  }

  getTotalDistributionPrincipal(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.distributionPrincipal || 0);
    }, 0);
  }

  getTotalCharges(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.charges || 0);
    }, 0);
  }

  getTotalOther(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.other || 0);
    }, 0);
  }

  getTotalEscrow(): number {
    if (!this.reports || this.reports.length === 0) {
      return 0;
    }

    return this.reports.reduce((total, report) => {
      return total + (report.escrow || 0);
    }, 0);
  }

  onDateRangeChange(event: any): void {
    this.showClearDateRangeIcon = true;
    if (event.start && event.end) {
      // Set start date to beginning of day (00:00:00)
      const startDate = new Date(event.start);
      startDate.setHours(0, 0, 0, 0);

      // Set end date to end of day (23:59:59)
      const endDate = new Date(event.end);
      endDate.setHours(23, 59, 59, 999);
      this.filterParams.startDate = this.datepipe.transform(startDate, 'yyyy-MM-dd HH:mm:ss') || '';
      this.filterParams.endDate = this.datepipe.transform(endDate, 'yyyy-MM-dd HH:mm:ss') || '';
      this.getList();
    }
  }

  onClearDateRange(event?: Event): void {
    const haveEndDate = this.filterParams.endDate !== null && this.filterParams.endDate !== undefined; // Check if endDate is defined and not null or undefined
    this.dateRangeValue = { start: new Date(), end: new Date() };
    delete this.filterParams.startDate;
    this.dateRangeInput.nativeElement.value = '';
    if (haveEndDate) {
      delete this.filterParams.endDate;
      this.getList();
    }
    this.showClearDateRangeIcon = false;
  }

  private generatePDFTable(doc: jsPDF): void {
    const startY = 30;

    switch (this.selectedReportType) {
      case ReportType.InvestmentPortfolio:
        this.generateInvestmentPortfolioTable(doc, startY);
        break;
      case ReportType.FundingActivity:
        this.generateFundingActivityTable(doc, startY);
        break;
      case ReportType.AccountActivity:
        this.generateAccountActivityTable(doc, startY);
        break;
      case ReportType.TrustActivity:
        this.generateTrustActivityTable(doc, startY);
        break;
      default:
        break;
    }
  }

  private generateInvestmentPortfolioTable(doc: jsPDF, startY: number): void {
    const headers = [
      'Loan Account',
      'Borrower Name',
      'Pct Owned',
      'Interest Rate',
      'Maturity Date',
      'Term Left',
      'Next Payment',
      'Regular Payment',
      'Loan Balance',
    ];

    const data = this.reports.map((report) => [
      report.loanNumber || '',
      report.borrowerName || '',
      `${(report.pctOwned || 0).toFixed(3)}%`,
      `${(report.lenderRate || 0).toFixed(3)}%`,
      report.maturityDate ? new Date(report.maturityDate).toLocaleDateString() : '',
      report.termLeft || '',
      report.nextPayment ? new Date(report.nextPayment).toLocaleDateString() : '',
      `$${(report.regularPayment || 0).toFixed(2)}`,
      `$${(report.principalBalance || 0).toFixed(2)}`,
    ]);

    // Add totals row
    const totalsRow = [
      '',
      '',
      '',
      `Current Portfolio Yield: ${this.getTotalInterestRate().toFixed(3)}%`,
      '',
      '',
      '',
      `$${this.getTotalRegularPayment().toFixed(2)}`,
      `$${this.getTotalLoanBalance().toFixed(2)}`,
    ];
    data.push(totalsRow);

    autoTable(doc, {
      head: [headers],
      body: data,
      startY: startY,
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: { fillColor: [0, 44, 36], textColor: 255 },
      didParseCell: (data) => {
        // Style the totals row
        if (data.row.index === this.reports.length) {
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.fillColor = [240, 240, 240];

          // Merge columns for "Current Portfolio Yield" text (columns 3-6: Interest Rate to Next Payment)
          if (data.column.index === 3) {
            data.cell.colSpan = 4;
            data.cell.styles.halign = 'left';
          } else if (data.column.index >= 4 && data.column.index <= 6) {
            data.cell.text = [];
          }
        }
      },
    });
  }

  private generateFundingActivityTable(doc: jsPDF, startY: number): void {
    const headers = ['Transaction Date', 'Reference', 'Loan Account', 'Borrower Name', 'Amount Funded'];

    const data = this.reports.map((report) => [
      report.paymentDate ? new Date(report.paymentDate).toLocaleDateString() : '',
      report.title || '',
      report.loanNumber || '',
      report.borrower || '',
      `$${(report.amount || 0).toFixed(2)}`,
    ]);

    // Add totals row
    const totalsRow = ['', '', '', '', `$${this.getTotalAmountFunded().toFixed(2)}`];
    data.push(totalsRow);

    autoTable(doc, {
      head: [headers],
      body: data,
      startY: startY,
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: { fillColor: [0, 44, 36], textColor: 255 },
      didParseCell: (data) => {
        // Style the totals row
        if (data.row.index === this.reports.length) {
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.fillColor = [240, 240, 240];
        }
      },
    });
  }

  private generateAccountActivityTable(doc: jsPDF, startY: number): void {
    const headers = [
      'Transaction Date',
      'Reference',
      'Loan Account',
      'Transaction Amount',
      'Serv. Fees',
      'GST',
      'Interest',
      'Distribution Principal',
      'Charges',
      'Other',
      'Escrow',
    ];

    const data = this.reports.map((report) => [
      report.transactionDate ? new Date(report.transactionDate).toLocaleDateString() : '',
      report.reference || '',
      report.loanAccount || '',
      `$${(report.transactionAmount || 0).toFixed(2)}`,
      `$${(report.servFees || 0).toFixed(2)}`,
      `$${(report.gst || 0).toFixed(2)}`,
      `$${(report.interest || 0).toFixed(2)}`,
      `$${(report.distributionPrincipal || 0).toFixed(2)}`,
      `$${(report.charges || 0).toFixed(2)}`,
      `$${(report.other || 0).toFixed(2)}`,
      `$${(report.escrow || 0).toFixed(2)}`,
    ]);

    // Add totals row
    const totalsRow = [
      '',
      '',
      'Total:',
      `$${this.getTotalTransactionAmount().toFixed(2)}`,
      `$${this.getTotalServFees().toFixed(2)}`,
      `$${this.getTotalGst().toFixed(2)}`,
      `$${this.getTotalInterest().toFixed(2)}`,
      `$${this.getTotalDistributionPrincipal().toFixed(2)}`,
      `$${this.getTotalCharges().toFixed(2)}`,
      `$${this.getTotalOther().toFixed(2)}`,
      `$${this.getTotalEscrow().toFixed(2)}`,
    ];
    data.push(totalsRow);

    autoTable(doc, {
      head: [headers],
      body: data,
      startY: startY,
      styles: { fontSize: 7, cellPadding: 1 },
      headStyles: { fillColor: [41, 128, 185], textColor: 255 },
      columnStyles: {
        3: { halign: 'right' },
        4: { halign: 'right' },
        5: { halign: 'right' },
        6: { halign: 'right' },
        7: { halign: 'right' },
        8: { halign: 'right' },
        9: { halign: 'right' },
        10: { halign: 'right' },
      },
      didParseCell: (data) => {
        // Style the totals row
        if (data.row.index === this.reports.length) {
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.fillColor = [240, 240, 240];
        }
      },
    });
  }

  private generateTrustActivityTable(doc: jsPDF, startY: number): void {
    const headers = [
      'Transaction Date',
      'Reference',
      'From/To Whom',
      'Description/Memo',
      'Amount Paid Out',
      'Amount Received',
      'Daily Balance',
    ];

    const data = this.reports.map((report) => [
      report.transactionDate ? new Date(report.transactionDate).toLocaleDateString() : '',
      report.reference || '',
      report.fromWhomReceivedOrToWhomPaid || '',
      report.descriptionMemo || '',
      `$${(report.amountPaidOut || 0).toFixed(2)}`,
      `$${(report.amountReceived || 0).toFixed(2)}`,
      `$${(report.dailyBalance || 0).toFixed(2)}`,
    ]);

    autoTable(doc, {
      head: [headers],
      body: data,
      startY: startY,
      styles: { fontSize: 8, cellPadding: 2 },
      headStyles: { fillColor: [41, 128, 185], textColor: 255 },
      columnStyles: {
        4: { halign: 'right' },
        5: { halign: 'right' },
        6: { halign: 'right' },
      },
    });
  }

  onExportReportToPDF(): void {
    if (!this.reports || this.reports.length === 0) {
      this.toast.warning('No data to export', 'Warning');
      return;
    }
    this.isDisabledExport = true;
    if (
      this.selectedReportType === ReportType.AccountActivity ||
      this.selectedReportType === ReportType.TrustActivity
    ) {
      this.exportReport();
      return;
    }
    try {
      const doc = new jsPDF();
      const reportTypeName = this.reportTypes.find((rt) => rt.id === this.selectedReportType)?.name || 'Report';

      // Add title
      doc.setFontSize(16);
      doc.text(`${reportTypeName}`, 14, 20);
      doc.text(`${this.reports.length} records`, doc.internal.pageSize.width - 14, 20, { align: 'right' });

      // Generate table based on report type
      this.generatePDFTable(doc);

      // Save the PDF
      const timestamp = Date.now();
      const fileName = `${reportTypeName.replace(/\s+/g, '_')}_${timestamp}.pdf`;
      doc.save(fileName);

      this.toast.success('PDF exported successfully', 'Success');
      setTimeout(() => {
        this.isDisabledExport = false;
      }, 1000);
    } catch (error) {
      console.error('Error generating PDF:', error);
      this.toast.danger('Failed to export PDF', 'Error');
      this.isDisabledExport = false;
    }
  }
}
