// Keep the styling minimal and consistent with investor-payments
// The component will inherit most styles from the global theme

@use "themes" as *;

.items-rows {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    .clear-icon {
        position: absolute;
        right: 0px;
        z-index: 10;
        color: var(--color-velvet-700);
        border: none;
        cursor: pointer;

        &:hover {
            color: var(--color-lime);
        }
        &:hover,
        &:focus,
        &:active {
            background-color: transparent;
            box-shadow: none;
        }
    }
}
.items-rows nb-select {
    // margin-right: 10px;
    min-width: 200px;
}

.items-rows div {
    margin-right: 5px;
}

.items-rows div {
    margin-bottom: 8px;
}

:host ::ng-deep {
    .p-datatable {
        .p-datatable-thead,
        .p-datatable-tbody {
            tr {
                display: table-row;
                th {
                    display: table-cell;
                }
                td {
                    display: table-cell;
                }
            }
        }
    }
}
