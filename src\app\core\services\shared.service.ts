import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import * as MobileCodes from '@assets/data/code.json';
import { UserRole } from '@core/helpers';
import { DashboardMessageType } from '@core/models/config';
import { NbToastrService } from '@nebular/theme';
import getUnicodeFlagIcon from 'country-flag-icons/unicode';
import jwt_decode from 'jwt-decode';
import { BehaviorSubject } from 'rxjs';
import { AuthenticationService } from './authentication.service';

@Injectable({
  providedIn: 'root',
})
export class FormParamDefinition {
  investorId?: number;
  investmentId?: number;
  userId?: number;
  changeTab?: boolean;
  tabId?: number;
  investmentTitle?: string;
  assetKeyDataId?: number;
  facility?: string;
}

export class UserIdDefinition {
  userId!: number;
}
export class MessagesParams {
  investorId?: number;
  showChat?: boolean | undefined;
  updateNotification?: boolean;
  chatUserId?: number;
}

export class Filters {
  startDate?: string;
  endDate?: string;
  roleId?: number;
  statusId?: number;
  pageNumber?: number;
  pageSize?: number;
  export?: boolean;
  search?: string;
  sortField?: string;
  sortOrder?: string;
  formRecordId?: any;
  investorId?: number;
  forApproval?: boolean;
  entityTypeId?: number;
  messageType?: DashboardMessageType;
  investmentId?: number;
  transactionTypeId?: number;
  documentType?: number;
  userId?: number;
  facilityStatusId?: number;
  facilityTypeId?: number;
  lenderId?: number;
  assetId?: number;
  documentTypeId?: number;
  facility?: string;
  assignedUserId?: number;
  taskStatus?: number;
}

export class Message {
  formRecordId?: number;
  userId?: number;
  message?: string;
  fileUrl?: string;
  messageType?: number;
  createdBy?: number;
  modifiedBy?: number;
  isAdmin?: true;
  isInternalNote?: true;
  files?: [
    {
      documentKey: string;
      fileTitle: string;
      fileName: string;
      fileData: {
        position: 0;
        readTimeout: 0;
        writeTimeout: 0;
      };
    },
  ];
  attachmentId?: number;
}

const toastClass = {
  toastClass: 'toastr-msg',
};

@Injectable({
  providedIn: 'root',
})
export class SharedService {
  private formParamSubject: BehaviorSubject<FormParamDefinition>;
  private userIdSubject: BehaviorSubject<UserIdDefinition>;
  showMessageSubject: BehaviorSubject<MessagesParams>;

  constructor(
    public datepipe: DatePipe,
    private titleService: Title,
    private authenticationService: AuthenticationService,
    private toastr: NbToastrService,
    private router: Router,
  ) {
    this.formParamSubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem('form') as string),
    ) as BehaviorSubject<FormParamDefinition>;
    this.userIdSubject = new BehaviorSubject<any>(
      JSON.parse(localStorage.getItem('userid') as string),
    ) as BehaviorSubject<UserIdDefinition>;

    this.showMessageSubject = new BehaviorSubject<any>(false);
  }

  today = new Date();
  x: any;

  public get getFormParamValue(): FormParamDefinition {
    return this.formParamSubject.value;
  }

  public get getUserIdValue(): UserIdDefinition {
    return this.userIdSubject.value;
  }

  setFormParamValue(setFormValue: FormParamDefinition): any {
    localStorage.setItem('form', JSON.stringify(setFormValue));
    this.formParamSubject.next(setFormValue);
  }
  setUserIdValue(setFormValue: UserIdDefinition): any {
    localStorage.setItem('userid', JSON.stringify(setFormValue));
    this.userIdSubject.next(setFormValue);
  }

  public get showMessageValue(): MessagesParams {
    return this.showMessageSubject.value;
  }

  setShowMessageValue(setFormValue: MessagesParams): any {
    this.showMessageSubject.next(setFormValue);
  }

  getFiltersFromDataTable(event: any, filterParams: Filters, utc = false): Filters {
    if (event.filters && event.filters?.facility) {
      filterParams.facility = event.filters?.facility.value;
    } else {
      delete filterParams.facility;
    }

    if (event.filters && event.filters?.statusId) {
      filterParams.statusId = event.filters?.statusId.value;
    } else {
      delete filterParams.statusId;
    }

    if (event.filters && event.filters?.facilityStatusId) {
      filterParams.facilityStatusId = event.filters?.facilityStatusId.value;
    } else {
      delete filterParams.facilityStatusId;
    }

    if (event.filters && event.filters?.facilityTypeId) {
      filterParams.facilityTypeId = event.filters?.facilityTypeId.value;
    } else {
      delete filterParams.facilityTypeId;
    }

    if (event.filters && event.filters?.lenderId) {
      filterParams.lenderId = event.filters?.lenderId.value;
    } else {
      delete filterParams.lenderId;
    }

    if (event.filters && event.filters?.documentTypeId) {
      filterParams.documentTypeId = event.filters?.documentTypeId.value;
    } else {
      delete filterParams.documentTypeId;
    }

    if (event.filters && event.filters?.entityTypeId) {
      filterParams.entityTypeId = event.filters?.entityTypeId.value;
    } else {
      delete filterParams.entityTypeId;
    }

    if (event.filters && event.filters?.entityTypeId) {
      filterParams.entityTypeId = event.filters?.entityTypeId.value;
    } else {
      delete filterParams.entityTypeId;
    }

    if (event.filters && event.filters?.roleId) {
      filterParams.roleId = event.filters?.roleId.value;
    } else {
      delete filterParams.roleId;
    }

    if (event.filters && event.filters?.global) {
      filterParams.search = event.filters?.global.value;
    } else {
      delete filterParams.search;
    }

    if (event.sortField && event.sortOrder) {
      filterParams.sortField = event.sortField as string;
      filterParams.sortOrder = event.sortOrder === 1 ? 'asc' : 'desc';
    } else {
      delete filterParams.sortField;
      delete filterParams.sortOrder;
    }

    if (
      (event.filters && event.filters?.paymentDate) ||
      event.filters?.submittedDate ||
      event.filters?.dateCreated ||
      event.filters?.date ||
      event.filters?.dueDate
    ) {
      let dateValue = null;

      if (event.filters.paymentDate) {
        dateValue = event.filters.paymentDate.value;
      } else if (event.filters.submittedDate) {
        dateValue = event.filters.submittedDate.value;
      } else if (event.filters.dateCreated) {
        dateValue = event.filters.dateCreated.value;
      } else if (event.filters.date) {
        dateValue = event.filters.date.value;
      } else if (event.filters.dueDate) {
        dateValue = event.filters.dueDate.value;
      }
      const { startDate, endDate } = this.getDateValue(dateValue, 1, utc);
      filterParams.startDate = startDate;
      filterParams.endDate = endDate;
    } else {
      delete filterParams.startDate;
      delete filterParams.endDate;
    }

    if (event.filters && event.filters?.investmentId) {
      filterParams.investmentId = event.filters?.investmentId.value;
    } else {
      delete filterParams.investmentId;
    }

    if (event.filters && event.filters?.transactionTypeId) {
      filterParams.transactionTypeId = event.filters?.transactionTypeId.value;
    } else {
      delete filterParams.transactionTypeId;
    }

    if (filterParams.assignedUserId && event.filters && event.filters?.assignedUserId?.value == 'All') {
      delete filterParams.assignedUserId;
    } else if (event.filters && event.filters?.assignedUserId && event.filters?.assignedUserId?.value != 'All') {
      filterParams.assignedUserId = event.filters?.assignedUserId.value.userId;
    } else {
      // ** keep default assignedUserId **
    }

    if (filterParams.taskStatus && !event.filters && !event.filters?.taskStatus) {
      // reload after edit task - All = Outstanding (Not completed) & Overdue
      delete filterParams.taskStatus;
    } else if (event.filters && event.filters?.taskStatus) {
      if (event.filters?.taskStatus.value.id == 'BEFORE_1_BOT') {
        // Overdue
        const dateValue = event.filters?.taskStatus.value.id;
        const { startDate, endDate } = this.getDateValue(dateValue, 1, true);
        filterParams.startDate = startDate;
        filterParams.endDate = endDate;
        delete filterParams.taskStatus;
      } else {
        // Completed
        filterParams.taskStatus = event.filters?.taskStatus.value.id;
      }
    } else {
      // Init default All = Outstanding (Not completed) & Overdue
      delete filterParams.taskStatus;
    }

    filterParams.pageSize = event.rows;
    filterParams.pageNumber = Number(event.first) / Number(event.rows);
    return filterParams;
  }

  getStatusState(status: string): string {
    if (status === 'new' || status === 'draft') {
      return 'info';
    } else if (status === 'submitted') {
      return 'primary';
    } else if (status === 'completed') {
      return 'success';
    } else if (status === 'withdrawn') {
      return 'danger';
    }
    return 'default';
  }

  numberWithCommas(amount: number): string {
    if (amount) {
      return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  }

  getDateFilterRows(): any[] {
    return [
      { name: 'All Time', value: null },
      { name: 'This Week', value: 'THIS_WEEK' },
      { name: 'This Month', value: 'THIS_MONTH' },
      // { name: 'This Financial Year', value: '' },
      { name: 'This Calendar Year', value: 'THIS_YEAR' },
      { name: 'Last Week', value: 'LAST_1_WEEK' },
      { name: 'Last Month', value: 'LAST_1_MONTH' },
      // { name: 'Last Financial Year', value: '' },
      { name: 'Last Calendar Year', value: 'LAST_1_YEAR' },
    ];
  }

  getDatefilterRowsActivities(): any[] {
    return [
      { name: 'All', value: null },
      { name: 'Overdue', value: 'BEFORE_1_BOT' }, // Overdue: beginning of time -> yesterday
      { name: 'Today', value: 'THIS_DAY' },
      { name: 'Tomorrow', value: 'NEXT_1_DAY' },
      { name: 'This Week', value: 'THIS_WEEK' },
      { name: 'Next Week', value: 'NEXT_1_WEEK' },
      { name: 'This Month', value: 'THIS_MONTH' },
      { name: 'Next Month', value: 'NEXT_1_MONTH' },
    ];
  }

  getDateValue(selectedOperator: string, n = 1, utc = false): any {
    /**
     * few db tables saves date as UTC e.g. '2022-12-31'
     * angular datepicker displays as local e.g. '2023-01-01'
     */
    const today = new Date(Date.now());
    if (utc) today.setDate(today.getDate() - 1);
    let startDate: Date = today;
    let endDate: Date = today;
    const components: string[] = selectedOperator.split('_');
    let startDateTemp;
    let endDateTemp;
    let dateRangeUnitIndex = 2;
    if (components.length > 1) {
      const parent = components[0];
      if (parent === 'THIS') {
        dateRangeUnitIndex = 1;
      }

      switch (parent) {
        case 'NEXT':
          switch (components[dateRangeUnitIndex]) {
            case 'DAY':
              startDate = this.addDays(today, 1);
              endDate = this.addDays(startDate, n - 1);
              break;
            case 'WEEK':
              startDateTemp = this.addWeeks(today, 1);
              startDate = this.startDateOfWeek(startDateTemp);
              if (utc) startDate.setDate(startDate.getDate() - 1); // UTC
              endDateTemp = this.addWeeks(startDate, n);
              endDate = this.addDays(endDateTemp, -1);
              break;
            case 'MONTH': {
              startDate = this.firstDayOfNextMonth(today);
              if (utc) startDate.setDate(startDate.getDate() - 1); // UTC
              const nmonth = startDate.getMonth() + (utc ? 2 : n);
              endDate = new Date(today.getFullYear(), nmonth, 0);
              break;
            }
            case 'QUARTER':
              startDate = this.firstDayOfNextQuarter(today);
              endDate = this.quarterEndDate(startDate, n);
              break;
            case 'YEAR':
              startDate = new Date(today.getFullYear() + 1, 0);
              endDate = new Date(startDate.getFullYear() + n, 0, 0);
              break;
          }
          break;
        case 'THIS':
          // THIS_DAY managed as default case
          switch (components[dateRangeUnitIndex]) {
            case 'WEEK':
              startDate = this.startDateOfWeek(today);
              if (utc) startDate.setDate(startDate.getDate() - 1); // UTC
              endDateTemp = this.addWeeks(startDate, 1);
              endDate = this.addDays(endDateTemp, -1);
              break;
            case 'MONTH':
              startDateTemp = this.addMonth(today, -1);
              startDate = startDate = this.firstDayOfNextMonth(startDateTemp);
              if (utc) startDate.setDate(startDate.getDate() - 1); // UTC
              endDateTemp = this.addMonth(startDate, 1);
              endDate = new Date(new Date().getFullYear(), endDateTemp.getMonth(), 0);
              if (utc) endDate.setDate(endDate.getDate() - 1); // UTC
              break;
            case 'QUARTER':
              startDateTemp = this.addQuarter(today, -1);
              startDate = this.firstDayOfNextQuarter(startDateTemp);
              endDate = this.quarterEndDate(startDate, 1);
              break;
            case 'YEAR':
              startDate = new Date(today.getFullYear(), 0);
              endDate = new Date(startDate.getFullYear() + 1, 0, 0);
              break;
          }
          break;

        case 'LAST':
          switch (components[dateRangeUnitIndex]) {
            case 'DAY':
              endDate = this.addDays(today, -1);
              startDate = this.addDays(endDate, -n + 1);
              break;
            case 'WEEK':
              endDateTemp = this.startDateOfWeek(today); // Fine della settimana scorsa
              endDate = this.addDays(endDateTemp, -1);
              startDateTemp = this.addWeeks(endDate, -n + 1); //
              startDate = this.startDateOfWeek(startDateTemp);
              break;
            case 'MONTH': {
              endDateTemp = this.addMonth(today, -1);
              const endMonthTemp1 = this.firstDayOfNextMonth(endDateTemp);
              endDate = this.addDays(endMonthTemp1, -1);
              startDate = this.addMonth(endDate, -n);
              break;
            }
            case 'QUARTER': {
              endDateTemp = this.addQuarter(today, -1);
              const endDataTemp1 = this.firstDayOfNextQuarter(endDateTemp);
              endDate = new Date(new Date().getFullYear(), endDataTemp1.getMonth(), 0);
              startDateTemp = this.addQuarter(endDataTemp1, -n);
              startDate = this.firstDayOfNextQuarter(startDateTemp);
              break;
            }
            case 'YEAR':
              endDate = new Date(today.getFullYear(), 0, 0);
              startDate = new Date(endDate.getFullYear() - (n - 1), 0);
              break;
          }
          break;
        case 'TO':
          switch (components[dateRangeUnitIndex]) {
            case 'MONTH':
              startDateTemp = this.addMonth(today, -1);
              startDate = this.firstDayOfNextMonth(startDateTemp);
              break;
            case 'QUARTER':
              startDateTemp = this.addQuarter(today, -1);
              startDate = this.firstDayOfNextQuarter(startDateTemp);
              break;
            case 'YEAR':
              startDate = new Date(today.getFullYear() - 1, 0);
              break;
          }
          endDate = today;
          break;

        case 'FROM':
          startDate = today;
          switch (components[dateRangeUnitIndex]) {
            case 'MONTH':
              endDateTemp = this.firstDayOfNextMonth(today);
              endDate = new Date(new Date().getFullYear(), endDateTemp.getMonth(), 0);
              break;
            case 'QUARTER':
              endDateTemp = this.firstDayOfNextQuarter(today);
              endDate = new Date(new Date().getFullYear(), endDateTemp.getMonth(), 0);
              break;
            case 'YEAR':
              endDate = new Date(today.getFullYear() + 1, 0, 0);
              break;
          }
          break;
        case 'BEFORE':
          switch (components[dateRangeUnitIndex]) {
            case 'MONTH':
              startDateTemp = this.addMonth(today, -1);
              startDate = this.firstDayOfNextMonth(startDateTemp);
              break;
            case 'QUARTER':
              startDateTemp = this.addQuarter(today, -1);
              startDate = this.firstDayOfNextQuarter(startDateTemp);
              break;
            case 'YEAR':
              startDate = new Date(today.getFullYear() - 1, 0);
              break;
            case 'BOT': //beginning of time
              startDate = new Date(1900, 0, 1);
              break;
          }
          endDate = this.addDays(today, -1);
          break;
      }

      // this.datepipe.transform(startDate, 'dd/MM/yyyy hh:mm:ss'),
      // this.datepipe.transform(endDate, 'dd/MM/yyyy hh:mm:ss');
      return {
        startDate: this.datepipe.transform(new Date(startDate).setHours(0, 0, 0, 0), 'yyyy-MM-dd HH:mm:ss'),
        endDate: this.datepipe.transform(new Date(endDate).setHours(23, 59, 59, 0), 'yyyy-MM-dd HH:mm:ss'),
      };
    }
  }

  private addDays(date: Date, days: number): Date {
    const myNewDate = new Date(date);
    myNewDate.setDate(myNewDate.getDate() + days);
    return myNewDate;
  }

  private addWeeks(date: Date, weeks: number): Date {
    return new Date(new Date().setDate(date.getDate() + weeks * 7));
  }

  private addMonth(date: Date, months: number): Date {
    const month = new Date().getMonth() + months;

    return new Date(new Date().getFullYear(), month);
  }

  private addQuarter(date: Date, quarters: number): Date {
    const myNewDate = new Date(date);
    myNewDate.setMonth(myNewDate.getMonth() + quarters * 3);
    return myNewDate;
  }

  private startDateOfWeek(date: Date): Date {
    const firstDateOfWeek = date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1);
    return new Date(new Date().getFullYear(), date.getMonth(), firstDateOfWeek);
  }

  private firstDayOfNextMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth() + 1, 1);
  }

  private firstDayOfNextQuarter(date: Date): Date {
    const quarter = Math.round(date.getMonth() / 3);
    const nextQuarter = quarter * 3;
    return new Date(date.getFullYear(), nextQuarter);
  }

  private quarterEndDate(startDate: Date, n: number): Date {
    const today = new Date(Date.now());
    const quarter = Math.round(startDate.getMonth() / 3);
    const nextEndQuarter = quarter * 3 + n * 3;
    return new Date(today.getFullYear(), nextEndQuarter, 0);
  }

  downloadFile(data: Blob, exportedFilename: string, fileData?: any): void {
    const blob = new Blob([data], {
      type: fileData ? fileData.contentType : 'application/octet-stream',
    });
    const url = URL.createObjectURL(blob);
    const blobAnchor = document.createElement('a');
    const dataURIAnchor = document.createElement('a');
    blobAnchor.download = dataURIAnchor.download = exportedFilename;
    blobAnchor.className = 'hidden';
    blobAnchor.href = url;
    // dataURIAnchor.href = dataURI;
    document.body.appendChild(blobAnchor);
    blobAnchor.target = '_blank';

    blobAnchor.onclick = () => {
      requestAnimationFrame(() => {
        URL.revokeObjectURL(url);
        setTimeout(() => blobAnchor.remove(), 300);
      });
    };

    blobAnchor.click();
  }

  download(data: Blob, exportedFilename: string, fileData?: any): void {
    const blob = new Blob([data], {
      type: fileData ? fileData.contentType : 'application/octet-stream',
    });
    const url = window.URL.createObjectURL(blob);

    // const newurl = this.sanitizer.bypassSecurityTrustResourceUrl(url);

    const anchor = document.createElement('a');
    anchor.href = url as any;
    if (!fileData) {
      anchor.download = exportedFilename;
    }
    const ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf('safari') !== -1) {
      if (ua.indexOf('chrome') > -1) {
        anchor.target = '_blank';
        anchor.click(); // Chrome
      } else {
        anchor.download = exportedFilename; // Safari
        anchor.target = '_blank';
        anchor.click(); // Chrome
      }
    } else if (ua.indexOf('firefox') !== -1) {
      this.downloadFile(data, exportedFilename, fileData);
    }

    // const windowReference = window.open();
    // if (!windowReference || windowReference.closed || typeof windowReference.closed === 'undefined') {
    //   alert('Please disable your Pop-up blocker and try again.');
    // } else if (windowReference) {
    //   windowReference.location.href = url as string;
    // }
  }

  timeAgo(time: any): any {
    if (!time) {
      return '';
    }

    switch (typeof time) {
      case 'number':
        break;
      case 'string':
        time = +new Date(time);
        break;
      case 'object':
        if (time.constructor === Date) {
          time = time.getTime();
        }
        break;
      default:
        time = +new Date();
    }
    const timeFormats = [
      [60, 'seconds', 1], // 60
      [120, '1 min ago', '1 min ago'], // 60*2
      [3600, 'min', 60], // 60*60, 60
      [7200, '1 hour ago', '1 hour ago'], // 60*60*2
      [86400, 'hours', 3600], // 60*60*24, 60*60
      [172800, 'Yesterday', 'Tomorrow'], // 60*60*24*2
      [604800, 'days', 86400], // 60*60*24*7, 60*60*24
      [1209600, 'Last week', 'Next week'], // 60*60*24*7*4*2
      [2419200, 'weeks', 604800], // 60*60*24*7*4, 60*60*24*7
      [4838400, 'Last month', 'Next month'], // 60*60*24*7*4*2
      [29030400, 'months', 2419200], // 60*60*24*7*4*12, 60*60*24*7*4
      [58060800, 'Last year', 'Next year'], // 60*60*24*7*4*12*2
      [2903040000, 'years', 29030400], // 60*60*24*7*4*12*100, 60*60*24*7*4*12
      [5806080000, 'Last century', 'Next century'], // 60*60*24*7*4*12*100*2
      [58060800000, 'centuries', 2903040000], // 60*60*24*7*4*12*100*20, 60*60*24*7*4*12*100
    ];
    let seconds = (+new Date() - time) / 1000;
    let token = 'ago';
    let listChoice = 1;

    if (seconds === 0) {
      return 'Just now';
    }
    if (seconds < 0) {
      seconds = Math.abs(seconds);
      token = 'ago';
      listChoice = 2;
    }
    let i = 0;
    let format: any;
    while ((format = timeFormats[i++])) {
      if (seconds < format[0]) {
        if (typeof format[2] === 'string') {
          return format[listChoice];
        } else {
          return Math.floor(seconds / format[2]) + ' ' + format[1] + ' ' + token;
        }
      }
    }
    return time;
  }

  loadImages(): void {
    const favicon = document.getElementById('favicon');
    if (favicon) {
      favicon.setAttribute('href', `assets/images/favicon.png`);
      this.setTitle('Sydney Wyde - Client Portal');
    }
  }

  private setTitle(newTitle: string): void {
    this.titleService.setTitle(newTitle);
  }

  public base64ToBlob(b64Data: any, contentType = '', sliceSize = 512): Blob {
    b64Data = b64Data.replace(/\s/g, ''); // IE compatibility...
    const byteCharacters = atob(b64Data);
    const byteArrays = [];
    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    return new Blob(byteArrays, { type: contentType });
  }

  isInvestor(): boolean {
    return this.authenticationService.userValue?.role === UserRole.Investor;
  }

  isAdmin(): boolean {
    return this.authenticationService.userValue?.role === UserRole.Administrator;
  }

  isOriginatorManager(): boolean {
    return this.authenticationService.userValue?.role === UserRole.OriginatorManager;
  }

  isManager(): boolean {
    return this.authenticationService.userValue?.role === UserRole.Manager;
  }

  isLender(): boolean {
    return this.authenticationService.userValue?.role === UserRole.Lender;
  }

  authenticate(token: string, data: any): void {
    if (data.success && data.payload.isLocked) {
      this.toastr.danger(data.error.message, 'Error!', toastClass);
    } else if (data.success && data.payload.isSuccess) {
      this.toastr.success('Authentication successful.', 'Success!', toastClass);

      this.saveURLToken(token);

      setTimeout(() => {
        if (this.isAdmin()) {
          this.authenticationService.getUserParams(token).subscribe((userParams: any) => {
            if (userParams.success && userParams.payload) {
              this.setUserIdValue({
                userId: userParams.payload.userId,
              });
            }
          });
        }
        this.router.navigateByUrl('/dashboard');
      }, 1000);
    } else if (!data.success && !data.payload) {
      this.toastr.danger(data.error.message, 'Error!', toastClass);
    } else {
      // this.error = true;
      this.toastr.danger('Token is invalid or expired please try again', 'Error!', toastClass);
    }
  }

  isInvestorStaffUsers(): boolean {
    return this.isAdmin() || this.isManager();
  }

  isAssetUsers(): boolean {
    return this.isAdmin() || this.isOriginatorManager() || this.isLender();
  }

  getEmailFromToken(): string {
    const decodedToken: any = jwt_decode(this.authenticationService.userValue?.token); // decode token
    return decodedToken.UserNameClaimToken;
  }

  async getUserNameFromGetUserAPI(): Promise<string> {
    const response = await this.authenticationService.getUserParams(this.getURLToken()).toPromise();
    return response.payload.name;
  }

  saveURLToken(token: string): void {
    localStorage.setItem('urlToken', token);
  }

  getURLToken(): string {
    return localStorage.getItem('urlToken') || '';
  }

  codeToFlag(code: string): any {
    // const base: number = 127462 - 65;
    // const cc = code.toUpperCase();
    // const res = String.fromCodePoint(...cc.split('').map(c => base + Number(c.charCodeAt(0))));
    // return 'data';
    return getUnicodeFlagIcon(code);
  }

  getMobileCodes(): any {
    return MobileCodes;
  }
}
