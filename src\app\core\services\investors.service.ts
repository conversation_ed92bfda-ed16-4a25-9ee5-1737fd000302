import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { HttpResponse } from '@core/models/response/http.response';
import {
  ReportsFundingActivityPayload,
  ReportsInvestmentPortfolioPayload,
} from '@core/models/response/reports.reponse';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ExportService } from './export.service';
import { Filters } from './shared.service';

export interface Investment {
  investorId?: number;
  investmentId?: number;
  investmentAmount: number;
  investmentUnit?: number;
  investmentTerm?: number;
  investmentReturn?: number;
  estimatedDistribution?: number;
  depositReference?: string;
  investmentDate?: Date;
  forApproval?: boolean;
  investmentStatusId?: number;
  investorConsent?: boolean;
}

export interface KeyData {
  investmentId: number;
  title?: string;
  borrower?: string;
  term?: number;
  startDate?: Date;
  totalOpportunity?: number;
  lvr?: number;
  minInvestment?: number;
  investmentReturn?: number;
  assetTypeId?: number;
  assetType?: string;
  investmentTypeId?: number;
  investmentType?: string;
  stateId?: number;
  suburb?: string;
  statusId?: number;
  isDeleted?: boolean;
  userId?: number;
}

export interface InvestorAccount {
  email?: string;
  entityType?: string;
  investorId?: number;
  name?: string;
  orgKey?: string;
  selected?: boolean;
  statusId?: number;
}

export interface Financial {
  id?: number;
  investorId?: number;
  bankAccountName: string;
  accountNo: string;
  bank: string;
  bsb: string;
  taxFileNo: string;
  taxFileNoExcemptionCode: string;
  host?: string;
}

@Injectable({
  providedIn: 'root',
})
export class InvestorsService {
  private accountSubject: BehaviorSubject<any>;
  public account: Observable<InvestorAccount>;

  constructor(
    private http: HttpClient,
    private exportService: ExportService,
  ) {
    this.accountSubject = new BehaviorSubject<any>(JSON.parse(localStorage.getItem('account') as string));
    this.account = this.accountSubject.asObservable();
  }

  public get accountValue(): InvestorAccount {
    return this.accountSubject.value;
  }

  setAccount(accountInfo: InvestorAccount): void {
    localStorage.setItem('account', JSON.stringify(accountInfo));
    this.accountSubject.next(accountInfo);
  }

  clearAccount(): void {
    localStorage.removeItem('account');
    this.accountSubject.next(null);
  }

  getInvestors(userFilters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/get-investors`, userFilters);
  }

  getInvestor(investorId: number): any {
    return this.http.get(`${environment.apiURL}/api/Investor/${investorId}`);
  }

  getFinancial(investorId: string): any {
    return this.http.get(`${environment.apiURL}/api/Investor/${investorId}/get-financial`, {
      params: { investorId },
    });
  }

  getInvestments(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/get-investments`, filters);
  }

  saveFinancial(financial: Financial): any {
    return this.http.post(`${environment.apiURL}/api/Investor/save-financial`, financial);
  }

  saveInvestment(investments: Investment[], financial?: Financial, data?: any): any {
    return this.http.post(`${environment.apiURL}/api/Investor/save-investment`, {
      investments,
      financial,
      investmentName: data ? data.investmentName : '',
      entityName: data ? data.entityName : '',
    });
  }

  deleteInvestment(params: any): any {
    return this.http.delete(`${environment.apiURL}/api/investor/delete-investment`, {
      body: params,
    } as any);
  }

  updateStatus(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/update-status`, filters);
  }

  getPayments(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/get-payments`, filters);
  }

  getDocuments(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/get-documents`, filters);
  }

  chathistory(filters: any): any {
    return this.http.post(`${environment.apiURL}/api/Investor/chathistory`, filters);
  }

  sendMessage(formData: any): any {
    const uploadURL = `${environment.apiURL}/api/Investor/sendmessage`;
    return this.http.post<any>(uploadURL, formData, {
      reportProgress: true,
      observe: 'events',
    });
  }

  getAccounts(): any {
    return this.http.get(`${environment.apiURL}/api/Investor/accounts`);
  }

  getNotificationCount(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/notification-count`, filters);
  }

  getUpdateNotification(filters: Filters): any {
    return this.http.post(`${environment.apiURL}/api/Investor/update-notification`, filters);
  }

  async getPaymentsExport(filters: any): Promise<void> {
    this.exportService.export(
      filters,
      `${environment.apiURL}/api/Investor/get-payments`,
      `Payments_${new Date().getTime()}.xlsx`,
    );
  }

  importPayments(formData: any): any {
    return this.http.post<any>(`${environment.apiURL}/api/Investor/upload-payment`, formData, {
      reportProgress: true,
      observe: 'events',
    });
  }

  deletePayment(paymentId: any): any {
    return this.http.delete(`${environment.apiURL}/api/Investor/delete-payment`, {
      body: { id: paymentId },
    } as any);
  }

  getReportsFundingActivity(filters: Filters): Observable<HttpResponse<ReportsFundingActivityPayload>> {
    return this.http.post<HttpResponse<ReportsFundingActivityPayload>>(
      `${environment.apiURL}/api/Report/funding-activity`,
      filters,
    );
  }

  getReportsInvestmentPortfolio(filters: Filters): Observable<HttpResponse<ReportsInvestmentPortfolioPayload>> {
    return this.http.post<HttpResponse<ReportsInvestmentPortfolioPayload>>(
      `${environment.apiURL}/api/Report/lender-portfolio`,
      filters,
    );
  }
}
