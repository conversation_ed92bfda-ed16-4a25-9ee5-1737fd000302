export interface ReportsFundingActivityResponse {
  id: number;
  paymentDate: string;
  title: string;
  loanNumber: string;
  amount: number;
  count: number;
  borrower: string;
}

export interface ReportsInvestmentPortfolioResponse {
  id: number;
  loanNumber: string;
  borrowerName: string;
  pctOwned: number;
  lenderRate: number;
  maturityDate: Date;
  termLeft: number;
  nextPayment: Date;
  regularPayment: number;
  principalBalance: number;
  count: number;
}

// New interface for the payload structure
export interface ReportsPayload<T> {
  investments: T[]; // Array of investment objects
  rows: number; // Total number of matching records
}

// Type aliases for specific payloads
export type ReportsFundingActivityPayload = ReportsPayload<ReportsFundingActivityResponse>;
export type ReportsInvestmentPortfolioPayload = ReportsPayload<ReportsInvestmentPortfolioResponse>;
